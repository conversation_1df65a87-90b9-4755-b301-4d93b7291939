import { describe, test, expect, beforeAll, afterAll, beforeEach } from '@jest/globals';
import {
  setupTestDatabase,
  prisma,
  createTestUser
} from '../setup/test-db-setup.js';

// We'll test the user ID suffix lookup logic directly
async function findUserByIdSuffix(suffix: string): Promise<{ id: string } | null> {
  if (suffix.length !== 8) {
    return null;
  }

  try {
    const user = await prisma.user.findFirst({
      where: {
        id: {
          endsWith: suffix
        }
      },
      select: {
        id: true
      }
    });

    return user;
  } catch (error) {
    return null;
  }
}

describe('User ID Suffix Lookup', () => {
  setupTestDatabase();

  let testUsers: any[] = [];

  beforeAll(async () => {
    // Create multiple test users to test suffix uniqueness
    testUsers = await Promise.all([
      createTestUser({ email: '<EMAIL>' }),
      createTestUser({ email: '<EMAIL>' }),
      createTestUser({ email: '<EMAIL>' })
    ]);

    // Wait a bit for database consistency
    await new Promise(resolve => setTimeout(resolve, 100));
  });

  test('should find user by correct 8-character suffix', async () => {
    // Create a fresh user for this test to avoid transaction issues
    const user = await createTestUser({ email: '<EMAIL>' });
    const suffix = user.id.slice(-8);

    const foundUser = await findUserByIdSuffix(suffix);

    expect(foundUser).toBeTruthy();
    expect(foundUser!.id).toBe(user.id);

    // Clean up
    await prisma.user.delete({ where: { id: user.id } }).catch(() => {});
  });

  test('should return null for non-existent suffix', async () => {
    const fakeSuffix = 'xxxxxxxx';
    
    const foundUser = await findUserByIdSuffix(fakeSuffix);
    
    expect(foundUser).toBeNull();
  });

  test('should return null for wrong length suffix', async () => {
    const user = testUsers[0];
    
    // Test too short
    const shortSuffix = user.id.slice(-7);
    const foundUserShort = await findUserByIdSuffix(shortSuffix);
    expect(foundUserShort).toBeNull();
    
    // Test too long
    const longSuffix = user.id.slice(-9);
    const foundUserLong = await findUserByIdSuffix(longSuffix);
    expect(foundUserLong).toBeNull();
    
    // Test empty
    const foundUserEmpty = await findUserByIdSuffix('');
    expect(foundUserEmpty).toBeNull();
  });

  test('should handle case sensitivity correctly', async () => {
    const user = testUsers[0];
    const suffix = user.id.slice(-8);
    
    // Test uppercase (should still work since CUID is case-sensitive but our lookup should be exact)
    const upperSuffix = suffix.toUpperCase();
    const foundUserUpper = await findUserByIdSuffix(upperSuffix);
    
    // This depends on your CUID format - typically they're lowercase
    // If your CUIDs are lowercase, uppercase won't match
    if (suffix === suffix.toLowerCase()) {
      expect(foundUserUpper).toBeNull();
    } else {
      expect(foundUserUpper).toBeTruthy();
    }
  });

  test('should verify suffix uniqueness assumption', async () => {
    // Create fresh users for this test
    const users = await Promise.all([
      createTestUser({ email: '<EMAIL>' }),
      createTestUser({ email: '<EMAIL>' }),
      createTestUser({ email: '<EMAIL>' })
    ]);

    try {
      // Get all suffixes from our test users
      const suffixes = users.map(user => user.id.slice(-8));

      // Check that all suffixes are unique (important for our routing logic)
      const uniqueSuffixes = new Set(suffixes);
      expect(uniqueSuffixes.size).toBe(suffixes.length);

      // Verify each suffix finds exactly one user
      for (const suffix of suffixes) {
        const foundUser = await findUserByIdSuffix(suffix);
        expect(foundUser).toBeTruthy();

        // Verify it's the correct user
        const expectedUser = users.find(u => u.id.endsWith(suffix));
        expect(foundUser!.id).toBe(expectedUser.id);
      }
    } finally {
      // Clean up
      for (const user of users) {
        await prisma.user.delete({ where: { id: user.id } }).catch(() => {});
      }
    }
  });

  test('should handle database errors gracefully', async () => {
    // Test with invalid characters that might cause DB issues
    const invalidSuffixes = [
      'abc\'def"', // SQL injection attempt
      'abc;DROP', // SQL injection attempt
      'abc\x00def', // Null byte
      'abc\ndef', // Newline
    ];
    
    for (const invalidSuffix of invalidSuffixes) {
      const result = await findUserByIdSuffix(invalidSuffix);
      // Should return null without throwing
      expect(result).toBeNull();
    }
  });

  test('should generate valid test email addresses', async () => {
    const user = await createTestUser({ email: '<EMAIL>' });

    try {
      const suffix = user.id.slice(-8);
      const testEmail = `${suffix}@test.emailconnect.eu`;

      // Verify email format
      expect(testEmail).toMatch(/^[a-z0-9]{8}@web\.xadi\.eu$/);

      // Verify suffix length
      expect(suffix).toHaveLength(8);

      // Verify suffix contains only valid CUID characters
      expect(suffix).toMatch(/^[a-z0-9]+$/);
    } finally {
      await prisma.user.delete({ where: { id: user.id } }).catch(() => {});
    }
  });

  test('should demonstrate collision probability', () => {
    // With 8 characters from CUID alphabet (25 chars: a-z except vowels, 0-9 except 0,1)
    // Probability of collision is very low but let's document it
    
    const cuidAlphabet = 'bcdfghjklmnpqrstvwxyz23456789'; // 25 characters
    const possibleCombinations = Math.pow(cuidAlphabet.length, 8);
    
    console.log(`Possible 8-character combinations: ${possibleCombinations.toLocaleString()}`);
    console.log(`That's ${(possibleCombinations / 1000000).toFixed(1)} million combinations`);
    
    // For reference: with 1 million users, collision probability is extremely low
    const millionUserCollisionProbability = 1 - Math.exp(-Math.pow(1000000, 2) / (2 * possibleCombinations));
    console.log(`Collision probability with 1M users: ${(millionUserCollisionProbability * 100).toFixed(8)}%`);
    
    expect(possibleCombinations).toBeGreaterThan(1000000); // Should be much larger than expected user base
  });
});
