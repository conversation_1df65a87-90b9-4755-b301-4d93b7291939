import { FastifyPluginAsync } from 'fastify';
import { userOrApi<PERSON>eyAuthMiddleware } from '../lib/auth.js';
import { DomainsController } from '../controllers/user/domains.controller.js';
import { domainSchemas } from '../schemas/user/domain.schemas.js';

const domainsController = new DomainsController();

export const domainsRoutes: FastifyPluginAsync = async (fastify) => {
  const errorResponseSchema = { $ref: 'ErrorResponse#' };

  // Get user's domains
  fastify.get('/domains', {
    preHandler: [userOrApiKeyAuthMiddleware],
    schema: {
      tags: ['User Domains'], 
      summary: 'List user domains',
      description: 'Retrieves a list of domains for the authenticated user.',
      response: {
        200: domainSchemas.DomainListResponse,
        401: { $ref: 'ErrorResponse#' },
        500: { ...errorResponseSchema, description: 'Failed to retrieve domain configurations.'}
      },
    }
  }, domainsController.getDomains.bind(domainsController));

  // Create new domain
  fastify.post('/domains', {
    preHandler: [userOrApiKeyAuthMiddleware],
    schema: {
      tags: ['User Domains'], 
      summary: 'Create a new domain',
      description: 'Registers a new domain for the authenticated user.',
      body: domainSchemas.CreateDomainRequest,
      response: {
        201: domainSchemas.CreateDomainResponse,
        400: errorResponseSchema, 
        401: { $ref: 'ErrorResponse#' }, 
        409: errorResponseSchema,
      },
    }
  }, domainsController.createDomain.bind(domainsController));

  // Get specific domain by name
  fastify.get('/domains/:domain', {
    preHandler: [userOrApiKeyAuthMiddleware],
    schema: {
      tags: ['User Domains'], 
      summary: 'Get domain by name',
      params: domainSchemas.DomainNameParam,
      response: { 
        200: domainSchemas.DomainDetailResponse, 
        401: { $ref: 'ErrorResponse#' }, 
        404: errorResponseSchema, 
        500: errorResponseSchema 
      }
    }
  }, domainsController.getDomain.bind(domainsController));

  // Update domain by ID
  fastify.put('/domains/:id', {
    preHandler: [userOrApiKeyAuthMiddleware],
    schema: {
      tags: ['User Domains'],
      summary: 'Update domain by ID',
      params: domainSchemas.DomainIdParam,
      body: domainSchemas.UpdateDomainRequest,
      response: {
        200: domainSchemas.UpdateDomainResponse,
        400: errorResponseSchema,
        401: { $ref: 'ErrorResponse#' },
        404: errorResponseSchema,
        500: errorResponseSchema
      }
    }
  }, domainsController.updateDomain.bind(domainsController));

  // Update domain status by ID
  fastify.put('/domains/:id/status', {
    preHandler: [userOrApiKeyAuthMiddleware],
    schema: {
      tags: ['User Domains'], 
      summary: 'Toggle domain status', 
      description: 'Toggle the active status of a domain by ID.',
      params: domainSchemas.DomainIdParam,
      body: domainSchemas.UpdateDomainStatusRequest,
      response: { 
        200: domainSchemas.UpdateDomainStatusResponse, 
        400: errorResponseSchema, 
        401: { $ref: 'ErrorResponse#' }, 
        404: errorResponseSchema, 
        500: errorResponseSchema 
      }
    }
  }, domainsController.updateDomainStatus.bind(domainsController));

  // Update domain webhook by ID
  fastify.put('/domains/:id/webhook', {
    preHandler: [userOrApiKeyAuthMiddleware],
    schema: {
      tags: ['User Domains'], 
      summary: 'Update domain webhook', 
      description: 'Update the webhook for a domain by ID.',
      params: domainSchemas.DomainIdParam,
      body: domainSchemas.UpdateDomainWebhookRequest,
      response: { 
        200: domainSchemas.UpdateDomainWebhookResponse, 
        400: errorResponseSchema, 
        401: { $ref: 'ErrorResponse#' }, 
        404: errorResponseSchema, 
        500: errorResponseSchema 
      }
    }
  }, domainsController.updateDomainWebhook.bind(domainsController));

  // Delete domain by name
  fastify.delete('/domains/:domain', {
    preHandler: [userOrApiKeyAuthMiddleware],
    schema: {
      tags: ['User Domains'], 
      summary: 'Delete domain by name',
      params: domainSchemas.DomainNameParam,
      response: { 
        200: domainSchemas.DeleteDomainResponse, 
        401: { $ref: 'ErrorResponse#' }, 
        404: errorResponseSchema, 
        500: errorResponseSchema
      }
    }
  }, domainsController.deleteDomain.bind(domainsController));

  // Verify domain by name
  fastify.post('/domains/:domain/verify', {
    preHandler: [userOrApiKeyAuthMiddleware],
    schema: {
      tags: ['User Domains'], 
      summary: 'Verify domain by name',
      params: domainSchemas.DomainNameParam,
      response: { 
        200: domainSchemas.VerifyDomainResponse, 
        400: errorResponseSchema,
        401: { $ref: 'ErrorResponse#' },
        404: errorResponseSchema,
        429: errorResponseSchema,
        500: errorResponseSchema 
      }
    }
  }, domainsController.verifyDomain.bind(domainsController));
};
