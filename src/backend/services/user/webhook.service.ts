import { prisma } from '../../lib/prisma.js';
import { logger } from '../../utils/logger.js';

export interface CreateWebhookData {
  name: string;
  url: string;
  description?: string;
  active?: boolean;
  webhookSecret?: string;
  generateSecret?: boolean;
  userId: string;
}

export interface UpdateWebhookData {
  name?: string;
  url?: string;
  description?: string;
  active?: boolean;
  webhookSecret?: string;
  removeSecret?: boolean;
  generateSecret?: boolean;
}

export class WebhookService {
  /**
   * Get all webhooks for a user
   */
  async getUserWebhooks(userId: string) {
    const webhooks = await prisma.webhook.findMany({
      where: { userId },
      include: {
        _count: {
          select: {
            domains: true,
            aliases: true
          }
        }
      },
      orderBy: { createdAt: 'desc' }
    });

    return webhooks.map(webhook => ({
      id: webhook.id,
      name: webhook.name,
      url: webhook.url,
      description: webhook.description,
      active: webhook.active,
      verified: webhook.verified,
      hasSecret: !!webhook.webhookSecret,
      createdAt: webhook.createdAt.toISOString(),
      updatedAt: webhook.updatedAt.toISOString(),
      domainCount: webhook._count.domains,
      aliasCount: webhook._count.aliases
    }));
  }

  /**
   * Get a specific webhook by ID for a user
   */
  async getWebhookById(webhookId: string, userId: string) {
    const webhook = await prisma.webhook.findFirst({
      where: {
        id: webhookId,
        userId
      },
      include: {
        domains: {
          select: {
            id: true,
            domain: true
          }
        },
        aliases: {
          select: {
            id: true,
            email: true
          }
        }
      }
    });

    if (!webhook) {
      return null;
    }

    return {
      id: webhook.id,
      name: webhook.name,
      url: webhook.url,
      description: webhook.description,
      active: webhook.active,
      verified: webhook.verified,
      hasSecret: !!webhook.webhookSecret,
      createdAt: webhook.createdAt.toISOString(),
      updatedAt: webhook.updatedAt.toISOString(),
      domains: webhook.domains,
      aliases: webhook.aliases
    };
  }

  /**
   * Create a new webhook
   */
  async createWebhook(data: CreateWebhookData) {
    // Check if webhook URL already exists for this user
    const existingWebhook = await prisma.webhook.findFirst({
      where: {
        url: data.url,
        userId: data.userId
      }
    });

    if (existingWebhook) {
      throw new Error('Webhook with this URL already exists');
    }

    // Handle webhook secret
    let finalWebhookSecret: string | null = null;
    if (data.generateSecret) {
      const crypto = await import('crypto');
      finalWebhookSecret = crypto.randomBytes(32).toString('hex');
    } else if (data.webhookSecret) {
      finalWebhookSecret = data.webhookSecret;
    }

    const webhook = await prisma.webhook.create({
      data: {
        name: data.name,
        url: data.url,
        description: data.description || null,
        active: data.active ?? true,
        webhookSecret: finalWebhookSecret,
        verified: false, // New webhooks start unverified
        userId: data.userId
      }
    });

    return {
      webhook: {
        id: webhook.id,
        name: webhook.name,
        url: webhook.url,
        description: webhook.description,
        active: webhook.active,
        verified: webhook.verified,
        hasSecret: !!webhook.webhookSecret,
        createdAt: webhook.createdAt.toISOString(),
        updatedAt: webhook.updatedAt.toISOString()
      },
      generatedSecret: data.generateSecret ? finalWebhookSecret : undefined
    };
  }

  /**
   * Update an existing webhook
   */
  async updateWebhook(webhookId: string, userId: string, updates: UpdateWebhookData) {
    // Check if webhook exists and belongs to user
    const existingWebhook = await prisma.webhook.findFirst({
      where: {
        id: webhookId,
        userId
      }
    });

    if (!existingWebhook) {
      throw new Error('Webhook not found');
    }

    // If URL is being updated, check for conflicts
    if (updates.url && updates.url !== existingWebhook.url) {
      const urlConflict = await prisma.webhook.findFirst({
        where: {
          url: updates.url,
          userId,
          id: { not: webhookId }
        }
      });

      if (urlConflict) {
        throw new Error('Another webhook with this URL already exists');
      }
    }

    // Handle webhook secret updates
    let webhookSecretUpdate: string | null | undefined = undefined;
    let generatedSecret: string | undefined = undefined;

    if (updates.removeSecret) {
      webhookSecretUpdate = null;
    } else if (updates.generateSecret) {
      const crypto = await import('crypto');
      generatedSecret = crypto.randomBytes(32).toString('hex');
      webhookSecretUpdate = generatedSecret;
    } else if (updates.webhookSecret !== undefined) {
      webhookSecretUpdate = updates.webhookSecret;
    }

    // Prepare update data
    const updateData: any = {
      ...updates,
      updatedAt: new Date()
    };

    // Remove secret-related fields from the main update
    delete updateData.webhookSecret;
    delete updateData.removeSecret;
    delete updateData.generateSecret;

    // Add webhook secret if it was modified
    if (webhookSecretUpdate !== undefined) {
      updateData.webhookSecret = webhookSecretUpdate;
    }

    // Reset verification status if URL or secret changed
    if (updates.url && updates.url !== existingWebhook.url) {
      updateData.verified = false;
      updateData.active = false; // Also set to inactive when URL changes
    } else if (webhookSecretUpdate !== undefined && webhookSecretUpdate !== existingWebhook.webhookSecret) {
      updateData.verified = false;
    }

    const updatedWebhook = await prisma.webhook.update({
      where: { id: webhookId },
      data: updateData
    });

    return {
      webhook: {
        id: updatedWebhook.id,
        name: updatedWebhook.name,
        url: updatedWebhook.url,
        description: updatedWebhook.description,
        active: updatedWebhook.active,
        verified: updatedWebhook.verified,
        hasSecret: !!updatedWebhook.webhookSecret,
        createdAt: updatedWebhook.createdAt.toISOString(),
        updatedAt: updatedWebhook.updatedAt.toISOString()
      },
      generatedSecret
    };
  }

  /**
   * Delete a webhook
   */
  async deleteWebhook(webhookId: string, userId: string) {
    // Check if webhook exists and belongs to user
    const webhook = await prisma.webhook.findFirst({
      where: {
        id: webhookId,
        userId
      },
      include: {
        _count: {
          select: {
            domains: true,
            aliases: true
          }
        }
      }
    });

    if (!webhook) {
      throw new Error('Webhook not found');
    }

    // Check if webhook is in use
    if (webhook._count.domains > 0 || webhook._count.aliases > 0) {
      throw new Error('Cannot delete webhook that is currently in use by domains or aliases');
    }

    await prisma.webhook.delete({
      where: { id: webhookId }
    });

    return { success: true };
  }

  /**
   * Verify a webhook
   */
  async verifyWebhook(webhookId: string, userId: string) {
    const webhook = await prisma.webhook.findFirst({
      where: {
        id: webhookId,
        userId
      }
    });

    if (!webhook) {
      throw new Error('Webhook not found');
    }

    // Generate verification token from last 5 characters of webhook ID
    const verificationToken = webhookId.slice(-5);
    const timestamp = Math.floor(Date.now() / 1000);

    const verificationPayload = {
      type: 'webhook_verification',
      verification_token: verificationToken,
      timestamp,
      webhook: {
        id: webhook.id,
        url: webhook.url
      }
    };

    // Send verification request to webhook
    const { queueWebhookDelivery } = await import('../queue.js');
    const jobId = await queueWebhookDelivery(
      webhook.url,
      verificationPayload as any,
      webhook.webhookSecret || undefined
    );

    logger.info({
      webhookId,
      webhookUrl: webhook.url,
      verificationToken,
      jobId
    }, 'Webhook verification request sent');

    // Note: The webhook is not marked as verified here
    // It should be verified when the user enters the correct token
    // or when we implement a callback endpoint for automatic verification

    return {
      id: webhook.id,
      verified: webhook.verified,
      verificationSent: true,
      verificationToken, // Return for frontend to validate
      jobId: jobId?.toString()
    };
  }

  /**
   * Complete webhook verification with token
   */
  async completeWebhookVerification(webhookId: string, userId: string, providedToken: string) {
    const webhook = await prisma.webhook.findFirst({
      where: {
        id: webhookId,
        userId
      }
    });

    if (!webhook) {
      throw new Error('Webhook not found');
    }

    // Generate expected token from webhook ID
    const expectedToken = webhookId.slice(-5);

    // Case-insensitive comparison to allow uppercase input
    if (providedToken.toLowerCase() !== expectedToken.toLowerCase()) {
      throw new Error('Invalid verification token');
    }

    // Mark webhook as verified
    const updatedWebhook = await prisma.webhook.update({
      where: { id: webhookId },
      data: { verified: true }
    });

    logger.info({
      webhookId,
      webhookUrl: webhook.url
    }, 'Webhook verification completed successfully');

    return {
      id: updatedWebhook.id,
      verified: updatedWebhook.verified,
      verificationCompleted: true
    };
  }
}
