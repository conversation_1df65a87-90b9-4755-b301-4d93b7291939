import { FastifyRequest, FastifyReply, Hook<PERSON>andlerDoneFunction } from 'fastify';
import { env } from '../config/env.js';
import { logger } from '../utils/logger.js';
import { AdminAuthService } from '../services/auth/admin-auth.service.js';
import { UserAuthService } from '../services/auth/user-auth.service.js';

// Initialize auth services
const adminAuthService = new AdminAuthService();
const userAuthService = new UserAuthService();

// Legacy handlers - these are now deprecated in favor of the new controllers
// They are kept for backward compatibility but should be replaced with the new controllers

export async function adminLoginHandler(request: FastifyRequest, reply: FastifyReply) {
  // This is a legacy handler - use AdminAuthController instead
  logger.warn('Using deprecated adminLoginHandler - consider migrating to AdminAuthController');

  const { username, password } = request.body as { username?: string; password?: string };

  if (!username || !password) {
    return reply.status(400).send({ error: 'Username and password are required' });
  }

  const authResult = await adminAuthService.authenticateAdmin({ username, password });
  if (!authResult.success) {
    const statusCode = authResult.error?.includes('required') ? 400 :
                      authResult.error?.includes('Invalid credentials') ? 401 : 500;
    return reply.status(statusCode).send({ error: authResult.error });
  }

  const tokenResult = adminAuthService.generateToken();
  if (!tokenResult.success) {
    return reply.status(500).send({ error: tokenResult.error });
  }

  const cookieConfig = adminAuthService.getCookieConfig();
  reply.setCookie('admin_token', tokenResult.token!, cookieConfig);

  return reply.send({ message: 'Admin login successful', token: tokenResult.token });
}

export function adminAuthMiddleware(request: FastifyRequest, reply: FastifyReply, done: HookHandlerDoneFunction) {
  const token = request.cookies.admin_token;

  if (!token) {
    if (request.url.startsWith('/admin/') && request.url !== '/admin/login' && request.method !== 'POST') {
      // Redirect to login page for GET requests to admin areas, except the login page itself
      return reply.status(302).redirect('/admin/login');
    }
    return reply.status(401).send({ error: 'Unauthorized: No token provided' });
  }

  const verifyResult = adminAuthService.verifyToken(token);
  if (!verifyResult.success) {
    request.log.warn({ err: verifyResult.error }, 'Admin JWT verification failed');
    if (request.url.startsWith('/admin/') && request.url !== '/admin/login' && request.method !== 'POST') {
      // Clear potentially invalid cookie and redirect
      reply.clearCookie('admin_token', { path: '/' });
      return reply.status(302).redirect('/admin/login');
    }
    return reply.status(401).send({ error: 'Unauthorized: Invalid token' });
  }

  // You can attach the decoded user to the request if needed
  // (request as any).admin = verifyResult.payload;
  done();
}

// =============================================================================
// USER AUTHENTICATION SYSTEM - Legacy handlers (use UserAuthController instead)
// =============================================================================

// Export user service functions for backward compatibility
export async function checkUserUsageLimit(userId: string): Promise<boolean> {
  return userAuthService.checkUserUsageLimit(userId);
}

export async function incrementUserEmailUsage(userId: string): Promise<void> {
  return userAuthService.incrementUserEmailUsage(userId);
}

export async function userRegisterHandler(request: FastifyRequest, reply: FastifyReply) {
  // This is a legacy handler - use UserAuthController instead
  logger.warn('Using deprecated userRegisterHandler - consider migrating to UserAuthController');

  // Handle both JSON and form data
  let userData;
  if (request.headers['content-type']?.includes('application/json')) {
    userData = request.body as { email?: string; password?: string; name?: string };
  } else {
    userData = request.body as any;
  }

  if (!userData.email || !userData.password) {
    return reply.status(400).send({ error: 'Email and password are required' });
  }

  const result = await userAuthService.registerUser({
    email: userData.email,
    password: userData.password,
    name: userData.name
  });

  if (!result.success) {
    const statusCode = result.error?.includes('required') ||
                      result.error?.includes('Invalid email') ||
                      result.error?.includes('Password must') ? 400 :
                      result.error?.includes('already exists') ? 409 : 500;
    return reply.status(statusCode).send({ error: result.error });
  }

  const cookieConfig = userAuthService.getCookieConfig();
  reply.setCookie('user_token', result.token!, cookieConfig);

  // Redirect to dashboard for form submissions
  if (!request.headers['content-type']?.includes('application/json')) {
    return reply.status(302).redirect('/domains');
  }

  return reply.status(201).send({
    message: 'Registration successful',
    user: result.user,
    token: result.token
  });
}

export async function userLoginHandler(request: FastifyRequest, reply: FastifyReply) {
  // This is a legacy handler - use UserAuthController instead
  logger.warn('Using deprecated userLoginHandler - consider migrating to UserAuthController');

  const { email, password } = request.body as { email?: string; password?: string };

  if (!email || !password) {
    return reply.status(400).send({ error: 'Email and password are required' });
  }

  const result = await userAuthService.loginUser({ email, password });

  if (!result.success) {
    const statusCode = result.error?.includes('required') ? 400 :
                      result.error?.includes('Invalid credentials') ? 401 : 500;
    return reply.status(statusCode).send({ error: result.error });
  }

  const cookieConfig = userAuthService.getCookieConfig();
  reply.setCookie('user_token', result.token!, cookieConfig);

  return reply.send({
    message: 'Login successful',
    user: result.user
    // Note: Token is only in httpOnly cookie for security
  });
}

export async function userAuthMiddleware(request: FastifyRequest, reply: FastifyReply) {
  const token = request.cookies.user_token;

  if (!token) {
    if (request.url.startsWith('/domains') && request.method !== 'POST') {
      // Redirect to login page for GET requests to dashboard areas
      return reply.status(302).redirect('/login');
    }
    return reply.status(401).send({ error: 'Unauthorized: No token provided' });
  }

  const verifyResult = userAuthService.verifyToken(token);
  if (!verifyResult.success) {
    request.log.warn({ err: verifyResult.error }, 'User JWT verification failed');
    if (request.url.startsWith('/domains') && request.method !== 'POST') {
      // Clear invalid cookie and redirect
      reply.clearCookie('user_token', { path: '/' });
      return reply.status(302).redirect('/login');
    }
    return reply.status(401).send({ error: 'Unauthorized: Invalid token' });
  }

  // SECURITY FIX: Verify user still exists in database
  try {
    const { PrismaClient } = await import('@prisma/client');
    const prisma = new PrismaClient();

    const user = await prisma.user.findUnique({
      where: { id: verifyResult.payload!.userId },
      select: { id: true, email: true }
    });

    if (!user) {
      request.log.warn({ userId: verifyResult.payload!.userId }, 'User no longer exists in database');
      // Clear invalid cookie and redirect/return error
      reply.clearCookie('user_token', { path: '/' });
      if (request.url.startsWith('/domains') && request.method !== 'POST') {
        return reply.status(302).redirect('/login');
      }
      return reply.status(401).send({
        statusCode: 401,
        error: 'Unauthorized',
        message: 'User not found'
      });
    }

    // Attach user info to request
    (request as any).user = {
      id: user.id,
      email: user.email,
    };

    await prisma.$disconnect();
  } catch (error) {
    request.log.error({ err: error }, 'Database error during user verification');
    return reply.status(500).send({
      statusCode: 500,
      error: 'Internal Server Error',
      message: 'Database error during user verification'
    });
  }
}

/**
 * API Key authentication middleware
 */
export async function apiKeyAuthMiddleware(request: FastifyRequest, reply: FastifyReply) {
  const apiKey = request.headers['x-api-key'] as string;

  if (!apiKey) {
    return reply.status(401).send({ error: 'Unauthorized: No API key provided' });
  }

  try {
    const { ApiKeyService } = await import('../services/auth/api-key.service.js');
    const apiKeyService = new ApiKeyService();

    const verifyResult = await apiKeyService.verifyApiKey(apiKey);
    if (!verifyResult.success) {
      return reply.status(401).send({ error: 'Unauthorized: Invalid API key' });
    }

    // Attach user info to request
    (request as any).user = {
      id: verifyResult.user!.id,
      email: verifyResult.user!.email,
    };
  } catch (error) {
    request.log.error({ err: error }, 'API key verification failed');
    return reply.status(500).send({ error: 'Internal server error' });
  }
}

/**
 * Combined authentication middleware that supports both cookie and API key authentication
 */
export async function userOrApiKeyAuthMiddleware(request: FastifyRequest, reply: FastifyReply) {
  const apiKey = request.headers['x-api-key'] as string;
  const authHeader = request.headers.authorization as string;
  const bearerToken = authHeader?.startsWith('Bearer ') ? authHeader.slice(7) : null;
  const cookieToken = request.cookies.user_token;
  const token = bearerToken || cookieToken;

  // Try API key authentication first
  if (apiKey) {
    try {
      const { ApiKeyService } = await import('../services/auth/api-key.service.js');
      const apiKeyService = new ApiKeyService();

      const verifyResult = await apiKeyService.verifyApiKey(apiKey);
      if (verifyResult.success) {
        // Attach user info to request
        (request as any).user = {
          id: verifyResult.user!.id,
          email: verifyResult.user!.email,
        };
        return;
      }
    } catch (error) {
      request.log.error({ err: error }, 'API key verification failed');
    }
  }

  // Fall back to token authentication (Bearer or cookie)
  if (!token) {
    if (request.url.startsWith('/domains') && request.method !== 'POST') {
      // Redirect to login page for GET requests to dashboard areas
      return reply.status(302).redirect('/login');
    }
    return reply.status(401).send({ error: 'Unauthorized: No token or API key provided' });
  }

  const verifyResult = userAuthService.verifyToken(token);
  if (!verifyResult.success) {
    request.log.warn({ err: verifyResult.error }, 'User JWT verification failed');
    if (request.url.startsWith('/domains') && request.method !== 'POST') {
      // Clear invalid cookie and redirect
      reply.clearCookie('user_token', { path: '/' });
      return reply.status(302).redirect('/login');
    }
    return reply.status(401).send({ error: 'Unauthorized: Invalid token' });
  }

  // SECURITY FIX: Verify user still exists in database
  try {
    const { PrismaClient } = await import('@prisma/client');
    const prisma = new PrismaClient();

    const user = await prisma.user.findUnique({
      where: { id: verifyResult.payload!.userId },
      select: { id: true, email: true }
    });

    if (!user) {
      request.log.warn({ userId: verifyResult.payload!.userId }, 'User no longer exists in database');
      // Clear invalid cookie and redirect/return error
      reply.clearCookie('user_token', { path: '/' });
      if (request.url.startsWith('/domains') && request.method !== 'POST') {
        return reply.status(302).redirect('/login');
      }
      return reply.status(401).send({
        statusCode: 401,
        error: 'Unauthorized',
        message: 'User not found'
      });
    }

    // Attach user info to request
    (request as any).user = {
      id: user.id,
      email: user.email,
    };

    await prisma.$disconnect();
  } catch (error) {
    request.log.error({ err: error }, 'Database error during user verification');
    return reply.status(500).send({
      statusCode: 500,
      error: 'Internal Server Error',
      message: 'Database error during user verification'
    });
  }
}