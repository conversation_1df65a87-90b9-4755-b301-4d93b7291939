<script setup lang="ts">
import { computed, ref, onMounted, onUnmounted } from 'vue'
import logoLight from '@/assets/images/logo-light.png'
import logoDark from '@/assets/images/logo-dark.png'

interface Props {
  size?: 'sm' | 'md' | 'lg'
  showText?: boolean
  textClass?: string
}

const props = withDefaults(defineProps<Props>(), {
  size: 'md',
  showText: true,
  textClass: 'text-base-content'
})

const sizeClasses = {
  sm: 'h-6',
  md: 'h-8',
  lg: 'h-12'
}

const textSizeClasses = {
  sm: 'text-lg',
  md: 'text-xl',
  lg: 'text-2xl'
}

// Reactive theme state
const currentTheme = ref('emerald')

// Determine which logo to use based on theme
const logoSrc = computed(() => {
  return currentTheme.value === 'dim' ? logoDark : logoLight
})

// Function to update theme from DOM
const updateTheme = () => {
  if (typeof document !== 'undefined') {
    const theme = document.documentElement.getAttribute('data-theme') || 'emerald'
    currentTheme.value = theme
  }
}

// MutationObserver to watch for theme changes
let observer: MutationObserver | null = null

onMounted(() => {
  // Initial theme check
  updateTheme()

  // Watch for changes to data-theme attribute
  if (typeof document !== 'undefined') {
    observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.type === 'attributes' && mutation.attributeName === 'data-theme') {
          updateTheme()
        }
      })
    })

    observer.observe(document.documentElement, {
      attributes: true,
      attributeFilter: ['data-theme']
    })
  }
})

onUnmounted(() => {
  if (observer) {
    observer.disconnect()
  }
})
</script>

<template>
  <div class="flex items-center space-x-3">
    <img
      :src="logoSrc"
      alt="EmailConnect.eu logo"
      :class="sizeClasses[props.size] + ' w-auto'"
    />
    <router-link to="/" v-if="props.showText" :class="[textSizeClasses[props.size], 'font-semibold', props.textClass]">
      EmailConnect
    </router-link>
  </div>
</template>
